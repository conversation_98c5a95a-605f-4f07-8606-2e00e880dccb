import 'package:news4humans/services/sqlite_service.dart';
import 'package:news4humans/services/hive_service.dart';

class SettingsRepository {
  final SQLiteService _sqliteService;
  final HiveService _hiveService;

  SettingsRepository({
    required SQLiteService sqliteService,
    required HiveService hiveService,
  })  : _sqliteService = sqliteService,
        _hiveService = hiveService;

  Future<Map<String, dynamic>> getSettings() async {
    // 1. Try to fetch from Hive cache
    Map<String, dynamic>? cachedSettings = await _hiveService.getCachedUserSettings();
    if (cachedSettings != null) {
      // Ensure preferredCategories is a valid JSON string or default to '[]'
      // This check is important here as it's the boundary from cache.
      if (cachedSettings[SQLiteService.colPreferredCategories] == null ||
          (cachedSettings[SQLiteService.colPreferredCategories] as String).isEmpty) {
        cachedSettings[SQLiteService.colPreferredCategories] = '[]';
      }
      return cachedSettings;
    }

    // 2. If not in cache, fetch from SQLite
    // Note: SQLiteService.getUserSettings() will be simplified to just fetch from DB.
    final Map<String, dynamic> settingsFromDb = await _sqliteService.getUserSettings();

    // 3. Cache the settings fetched from SQLite
    // The settingsFromDb should already have preferredCategories handled by the (soon to be refactored) SQLiteService.getUserSettings
    await _hiveService.cacheUserSettings(settingsFromDb);

    return settingsFromDb;
  }

  Future<void> updateSetting(String key, dynamic value) async {
    // Update the setting in SQLite
    // Note: SQLiteService.updateSetting() will be simplified.
    await _sqliteService.updateSetting(key, value);

    // Clear the user settings cache in Hive
    await _hiveService.clearUserSettingsCache();
  }

  // Example of specific setting methods (optional for now)
  // Future<String> getTheme() async {
  //   final settings = await getSettings();
  //   return settings[SQLiteService.colTheme] as String? ?? 'system';
  // }

  // Future<void> setTheme(String theme) async {
  //   await updateSetting(SQLiteService.colTheme, theme);
  // }

  Future<DateTime?> getLastSyncTimestamp(String dataType) async {
    final settings = await getSettings(); // Uses cache-aware getter
    String? timestampString;

    if (dataType == 'articles') {
      timestampString = settings[SQLiteService.colLastArticlesSyncTimestamp] as String?;
    } else if (dataType == 'filters') {
      timestampString = settings[SQLiteService.colLastFiltersSyncTimestamp] as String?;
    }
    // Add more dataTypes here if needed

    if (timestampString != null) {
      return DateTime.tryParse(timestampString);
    }
    return null;
  }

  Future<void> updateLastSyncTimestamp(String dataType, DateTime timestamp) async {
    String? keyToUpdate;
    if (dataType == 'articles') {
      keyToUpdate = SQLiteService.colLastArticlesSyncTimestamp;
    } else if (dataType == 'filters') {
      keyToUpdate = SQLiteService.colLastFiltersSyncTimestamp;
    }
    // Add more dataTypes here if needed

    if (keyToUpdate != null) {
      await updateSetting(keyToUpdate, timestamp.toIso8601String());
      // updateSetting already clears the general user settings cache,
      // so the new timestamp will be included next time getSettings is called.
    } else {
      // Handle unknown dataType if necessary, e.g., throw an error or log
      print('SettingsRepository: Unknown dataType for updateLastSyncTimestamp: $dataType');
    }
  }
}
