import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:news4humans/repositories/settings_repository.dart';
import 'package:news4humans/services/sqlite_service.dart'; // For static col names
import 'package:news4humans/services/hive_service.dart';
import '../mocks/mocks.dart'; // Your generated or manual mocks

void main() {
  late MockSQLiteService mockSQLiteService;
  late MockHiveService mockHiveService;
  late SettingsRepository settingsRepository;

  // Sample settings map for consistency
  final Map<String, dynamic> sampleSettingsFromDb = {
    SQLiteService.colTheme: 'dark',
    SQLiteService.colLanguage: 'en',
    SQLiteService.colHasConsented: 1,
    SQLiteService.colPreferredCategories: '["news","sports"]',
    SQLiteService.colLastArticlesSyncTimestamp: DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
    SQLiteService.colLastFiltersSyncTimestamp: DateTime.now().subtract(const Duration(hours: 12)).toIso8601String(),
  };

  final Map<String, dynamic> defaultSettingsMap = {
    SQLiteService.colTheme: 'system',
    SQLiteService.colLanguage: 'en',
    SQLiteService.colHasConsented: 0,
    SQLiteService.colPreferredCategories: '[]',
    SQLiteService.colLastArticlesSyncTimestamp: null,
    SQLiteService.colLastFiltersSyncTimestamp: null,
  };


  setUp(() {
    mockSQLiteService = MockSQLiteService();
    mockHiveService = MockHiveService();
    settingsRepository = SettingsRepository(
      sqliteService: mockSQLiteService,
      hiveService: mockHiveService,
    );

    // Default stub for SQLiteService.getUserSettings if not overridden by a specific test
    when(mockSQLiteService.getUserSettings()).thenAnswer((_) async => Map<String,dynamic>.from(defaultSettingsMap));
    // Default stub for HiveService.getCachedUserSettings if not overridden
    when(mockHiveService.getCachedUserSettings()).thenAnswer((_) async => null);
    // Default stub for cacheUserSettings
    when(mockHiveService.cacheUserSettings(any)).thenAnswer((_) async => Future.value());
    // Default stub for clearUserSettingsCache
    when(mockHiveService.clearUserSettingsCache()).thenAnswer((_) async => Future.value());
    // Default stub for SQLiteService.updateSetting
    when(mockSQLiteService.updateSetting(any, any)).thenAnswer((_) async => Future.value());

  });

  group('SettingsRepository Tests', () {
    group('getSettings', () {
      test('Scenario 1: Hive cache hit', () async {
        // Arrange
        when(mockHiveService.getCachedUserSettings()).thenAnswer((_) async => Map<String,dynamic>.from(sampleSettingsFromDb));

        // Act
        final settings = await settingsRepository.getSettings();

        // Assert
        expect(settings, sampleSettingsFromDb);
        verify(mockHiveService.getCachedUserSettings()).called(1);
        verifyNever(mockSQLiteService.getUserSettings());
        verifyNever(mockHiveService.cacheUserSettings(any)); // Should not cache if hit
      });

      test('Scenario 2: Hive cache miss, SQLite hit', () async {
        // Arrange
        when(mockHiveService.getCachedUserSettings()).thenAnswer((_) async => null); // Cache miss
        when(mockSQLiteService.getUserSettings()).thenAnswer((_) async => Map<String,dynamic>.from(sampleSettingsFromDb));

        // Act
        final settings = await settingsRepository.getSettings();

        // Assert
        expect(settings, sampleSettingsFromDb);
        verify(mockHiveService.getCachedUserSettings()).called(1);
        verify(mockSQLiteService.getUserSettings()).called(1);
        verify(mockHiveService.cacheUserSettings(sampleSettingsFromDb)).called(1);
      });

      test('Handles null preferredCategories from cache and DB by defaulting to []', () async {
        final settingsWithNullCategories = Map<String,dynamic>.from(sampleSettingsFromDb);
        settingsWithNullCategories[SQLiteService.colPreferredCategories] = null;

        // Test cache miss path
        when(mockHiveService.getCachedUserSettings()).thenAnswer((_) async => null);
        when(mockSQLiteService.getUserSettings()).thenAnswer((_) async => settingsWithNullCategories);

        var settings = await settingsRepository.getSettings();
        expect(settings[SQLiteService.colPreferredCategories], '[]');
        verify(mockHiveService.cacheUserSettings(argThat(predicate<Map<String,dynamic>>((map) => map[SQLiteService.colPreferredCategories] == '[]')))).called(1);

        // Reset calls and test cache hit path
        clearInteractions(mockHiveService);
        clearInteractions(mockSQLiteService);
        when(mockHiveService.getCachedUserSettings()).thenAnswer((_) async => settingsWithNullCategories);
         when(mockSQLiteService.getUserSettings()).thenAnswer((_) async => Map<String,dynamic>.from(sampleSettingsFromDb)); // Should not be called


        settings = await settingsRepository.getSettings();
        expect(settings[SQLiteService.colPreferredCategories], '[]');
        verify(mockHiveService.getCachedUserSettings()).called(1);
        verifyNever(mockSQLiteService.getUserSettings());

      });
    });

    group('updateSetting', () {
      test('Calls SQLiteService.updateSetting and clears Hive cache', () async {
        const key = SQLiteService.colTheme;
        const value = 'light';

        // Act
        await settingsRepository.updateSetting(key, value);

        // Assert
        verify(mockSQLiteService.updateSetting(key, value)).called(1);
        verify(mockHiveService.clearUserSettingsCache()).called(1);
      });
    });

    group('Sync Timestamps', () {
      test('getLastSyncTimestamp - articles - timestamp exists', () async {
        final expectedTime = DateTime.now().subtract(const Duration(days: 1));
        final settingsWithTimestamp = Map<String,dynamic>.from(sampleSettingsFromDb);
        settingsWithTimestamp[SQLiteService.colLastArticlesSyncTimestamp] = expectedTime.toIso8601String();

        when(mockHiveService.getCachedUserSettings()).thenAnswer((_) async => settingsWithTimestamp);

        final timestamp = await settingsRepository.getLastSyncTimestamp('articles');

        expect(timestamp, isNotNull);
        expect(timestamp!.toIso8601String(), expectedTime.toIso8601String());
      });

      test('getLastSyncTimestamp - articles - timestamp does not exist or is null', () async {
         final settingsWithoutTimestamp = Map<String,dynamic>.from(sampleSettingsFromDb);
        settingsWithoutTimestamp[SQLiteService.colLastArticlesSyncTimestamp] = null;
        when(mockHiveService.getCachedUserSettings()).thenAnswer((_) async => settingsWithoutTimestamp);

        final timestamp = await settingsRepository.getLastSyncTimestamp('articles');
        expect(timestamp, isNull);
      });

      test('getLastSyncTimestamp - unknown dataType returns null', () async {
        when(mockHiveService.getCachedUserSettings()).thenAnswer((_) async => sampleSettingsFromDb);
        final timestamp = await settingsRepository.getLastSyncTimestamp('unknown_type');
        expect(timestamp, isNull);
      });

      test('updateLastSyncTimestamp - articles', () async {
        final timestampToSet = DateTime.now();

        // Act
        await settingsRepository.updateLastSyncTimestamp('articles', timestampToSet);

        // Assert
        verify(mockSQLiteService.updateSetting(
          SQLiteService.colLastArticlesSyncTimestamp,
          timestampToSet.toIso8601String(),
        )).called(1);
        verify(mockHiveService.clearUserSettingsCache()).called(1);
      });

      test('updateLastSyncTimestamp - filters', () async {
        final timestampToSet = DateTime.now();
        await settingsRepository.updateLastSyncTimestamp('filters', timestampToSet);
        verify(mockSQLiteService.updateSetting(
          SQLiteService.colLastFiltersSyncTimestamp,
          timestampToSet.toIso8601String(),
        )).called(1);
        verify(mockHiveService.clearUserSettingsCache()).called(1);
      });

      test('updateLastSyncTimestamp - unknown dataType does nothing to DB/Cache', () async {
        await settingsRepository.updateLastSyncTimestamp('unknown_data', DateTime.now());
        verifyNever(mockSQLiteService.updateSetting(any, any));
        verifyNever(mockHiveService.clearUserSettingsCache());
      });
    });
  });
}
