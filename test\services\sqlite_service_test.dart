import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:news4humans/services/sqlite_service.dart';
import 'package:news4humans/models/article.dart';
import 'package:news4humans/models/filter.dart';
import 'dart:convert'; // For jsonEncode in helper

// Helper method to create a sample Article
Article createSampleArticle({
  String url = 'http://example.com/article1',
  String title = 'Sample Article 1',
  String? description = 'This is a sample article description.',
  String? content = 'Full content of the sample article.',
  String? author = '<PERSON>',
  String? source = 'Sample News',
  String? category = 'Technology',
  String? urlToImage = 'http://example.com/image1.jpg',
  DateTime? publishedAt,
  int readTime = 5,
  bool isSaved = false,
  DateTime? savedAt,
  DateTime? updatedAt,
}) {
  return Article(
    url: url,
    title: title,
    description: description,
    content: content,
    author: author,
    source: source,
    category: category,
    urlToImage: urlToImage,
    publishedAt: publishedAt ?? DateTime.now().subtract(const Duration(days: 1)),
    readTime: readTime,
    isSaved: isSaved,
    savedAt: savedAt,
    updatedAt: updatedAt,
  );
}

// Helper method to create a sample Filter
Filter createSampleFilter({
  String id = 'filter1',
  String name = 'Tech Filter',
  String description = 'Filters for technology news.',
  Map<String, dynamic>? parameters,
  bool isAiGenerated = false,
  DateTime? createdAt,
  bool isEnabled = true,
  DateTime? updatedAt,
}) {
  return Filter(
    id: id,
    name: name,
    description: description,
    parameters: parameters ?? {'keyword': 'flutter'},
    isAiGenerated: isAiGenerated,
    createdAt: createdAt ?? DateTime.now().subtract(const Duration(hours: 1)),
    isEnabled: isEnabled,
    updatedAt: updatedAt,
  );
}


void main() {
  late SQLiteService sqliteService;

  // Initialize FFI for sqflite at the start of all tests
  setUpAll(() {
    sqfliteFfiInit();
    // Change the default factory for all unit tests
    databaseFactory = databaseFactoryFfi;
  });

  setUp(() async {
    sqliteService = SQLiteService();
    // Ensure a fresh in-memory database for each test by re-initializing.
    // The `initDB(null)` or `initDB(':memory:')` should use the FFI factory.
    // Passing ':memory:' is often more explicit for FFI.
    await sqliteService.initDB(':memory:');
  });

  tearDown(() async {
    // Close the database after each test to ensure isolation
    await sqliteService.closeDb();
  });

  group('SQLiteService Tests', () {
    group('Articles', () {
      test('Save and Get Article', () async {
        final article = createSampleArticle(url: 'test/article1');
        await sqliteService.saveArticle(article);

        final retrievedArticle = await sqliteService.getArticle('test/article1');

        expect(retrievedArticle, isNotNull);
        expect(retrievedArticle!.url, article.url);
        expect(retrievedArticle.title, article.title);
        expect(retrievedArticle.updatedAt, isNotNull); // saveArticle sets this
      });

      test('Get All Articles', () async {
        final article1 = createSampleArticle(url: 'test/all1');
        final article2 = createSampleArticle(url: 'test/all2', title: 'Second Article');
        await sqliteService.saveArticle(article1);
        await sqliteService.saveArticle(article2);

        final allArticles = await sqliteService.getAllArticles();
        expect(allArticles.length, 2);
        expect(allArticles.any((a) => a.url == 'test/all1'), isTrue);
        expect(allArticles.any((a) => a.url == 'test/all2'), isTrue);
      });

      test('Get Saved Articles', () async {
        final savedArticle = createSampleArticle(url: 'test/saved1', isSaved: true, savedAt: DateTime.now());
        final unsavedArticle = createSampleArticle(url: 'test/unsaved1', isSaved: false);
        await sqliteService.saveArticle(savedArticle); // saveArticle in service now uses article.isSaved
        await sqliteService.saveArticle(unsavedArticle);

        final savedArticles = await sqliteService.getSavedArticles();
        expect(savedArticles.length, 1);
        expect(savedArticles.first.url, 'test/saved1');
        expect(savedArticles.first.isSaved, isTrue);
      });

      test('Update Article Saved Status', () async {
        final article = createSampleArticle(url: 'test/statusUpdate', isSaved: false);
        await sqliteService.saveArticle(article);

        var retrieved = await sqliteService.getArticle('test/statusUpdate');
        expect(retrieved!.isSaved, isFalse);
        expect(retrieved.savedAt, isNull);

        final savedTime = DateTime.now();
        await sqliteService.updateArticleSavedStatus('test/statusUpdate', true, savedTime);
        retrieved = await sqliteService.getArticle('test/statusUpdate');
        expect(retrieved!.isSaved, isTrue);
        // Compare milliseconds because DB might not store full microsecond precision
        expect(retrieved.savedAt!.millisecondsSinceEpoch, savedTime.millisecondsSinceEpoch);

        await sqliteService.updateArticleSavedStatus('test/statusUpdate', false, null);
        retrieved = await sqliteService.getArticle('test/statusUpdate');
        expect(retrieved!.isSaved, isFalse);
        expect(retrieved.savedAt, isNull);
      });

      test('Delete Article', () async {
        final article = createSampleArticle(url: 'test/delete1');
        await sqliteService.saveArticle(article);
        var retrieved = await sqliteService.getArticle('test/delete1');
        expect(retrieved, isNotNull);

        await sqliteService.deleteArticle('test/delete1');
        retrieved = await sqliteService.getArticle('test/delete1');
        expect(retrieved, isNull);
      });

      test('Delete All Articles', () async {
        await sqliteService.saveArticle(createSampleArticle(url: 'test/delAll1'));
        await sqliteService.saveArticle(createSampleArticle(url: 'test/delAll2'));

        await sqliteService.deleteAllArticles();
        final articles = await sqliteService.getAllArticles();
        expect(articles, isEmpty);
      });

      test('Article updatedAt timestamp', () async {
        final article = createSampleArticle(url: 'test/updatedAtCheck');
        await sqliteService.saveArticle(article);
        final retrieved1 = await sqliteService.getArticle('test/updatedAtCheck');
        final firstUpdateTime = retrieved1!.updatedAt;
        expect(firstUpdateTime, isNotNull);

        await Future.delayed(const Duration(milliseconds: 10)); // Ensure time progresses

        // Re-saving the same article (even without changes to content) should update its 'updatedAt'
        await sqliteService.saveArticle(retrieved1);
        final retrieved2 = await sqliteService.getArticle('test/updatedAtCheck');
        expect(retrieved2!.updatedAt, isNotNull);
        expect(retrieved2.updatedAt!.isAfter(firstUpdateTime!), isTrue, reason: "updatedAt should advance on re-save");
      });
    });

    group('Filters', () {
      test('Save and Get Filter', () async {
        final filter = createSampleFilter(id: 'f_get1');
        await sqliteService.saveFilter(filter);

        final retrievedFilter = await sqliteService.getFilter('f_get1');
        expect(retrievedFilter, isNotNull);
        expect(retrievedFilter!.id, filter.id);
        expect(retrievedFilter.name, filter.name);
        expect(retrievedFilter.createdAt.toIso8601String(), filter.createdAt.toIso8601String());
        expect(retrievedFilter.updatedAt, isNotNull); // saveFilter (via toDbMap) sets this
      });

      test('Get All Filters', () async {
        final filter1 = createSampleFilter(id: 'f_all1');
        final filter2 = createSampleFilter(id: 'f_all2', name: 'Another Filter');
        await sqliteService.saveFilter(filter1);
        await sqliteService.saveFilter(filter2);

        final allFilters = await sqliteService.getAllFilters();
        expect(allFilters.length, 2);
        expect(allFilters.any((f) => f.id == 'f_all1'), isTrue);
        expect(allFilters.any((f) => f.id == 'f_all2'), isTrue);
      });

      test('Delete Filter', () async {
        final filter = createSampleFilter(id: 'f_del1');
        await sqliteService.saveFilter(filter);
        var retrieved = await sqliteService.getFilter('f_del1');
        expect(retrieved, isNotNull);

        await sqliteService.deleteFilter('f_del1');
        retrieved = await sqliteService.getFilter('f_del1');
        expect(retrieved, isNull);
      });

      test('Delete All Filters', () async {
        await sqliteService.saveFilter(createSampleFilter(id: 'f_delAll1'));
        await sqliteService.saveFilter(createSampleFilter(id: 'f_delAll2'));

        await sqliteService.deleteAllFilters();
        final filters = await sqliteService.getAllFilters();
        expect(filters, isEmpty);
      });

      test('Filter timestamps and boolean fields', () async {
        final now = DateTime.now();
        final filter = createSampleFilter(
          id: 'f_fields',
          createdAt: now,
          isAiGenerated: true,
          isEnabled: false
        );
        await sqliteService.saveFilter(filter);
        final retrieved = await sqliteService.getFilter('f_fields');

        expect(retrieved, isNotNull);
        expect(retrieved!.isAiGenerated, isTrue);
        expect(retrieved.isEnabled, isFalse);
        expect(retrieved.createdAt.toIso8601String(), now.toIso8601String());
        expect(retrieved.updatedAt, isNotNull);
        // Check if updatedAt is close to now (or createdAt if model defaults updatedAt to createdAt on creation)
        expect(retrieved.updatedAt!.difference(now).inSeconds < 2, isTrue);

        await Future.delayed(const Duration(milliseconds: 10));
        // Re-saving should update `updatedAt`
        await sqliteService.saveFilter(retrieved.copyWith(name: "Updated Name"));
        final retrieved2 = await sqliteService.getFilter('f_fields');
        expect(retrieved2!.updatedAt!.isAfter(retrieved.updatedAt!), isTrue);
      });
    });

    group('UserSettings', () {
      test('Get User Settings - Default values on first call', () async {
        final settings = await sqliteService.getUserSettings();
        expect(settings[SQLiteService.colTheme], 'system');
        expect(settings[SQLiteService.colLanguage], 'en');
        expect(settings[SQLiteService.colHasConsented], 0);
        expect(settings[SQLiteService.colPreferredCategories], '[]');
        expect(settings[SQLiteService.colLastArticlesSyncTimestamp], isNull);
        expect(settings[SQLiteService.colLastFiltersSyncTimestamp], isNull);
      });

      test('Update and Get User Setting - Theme', () async {
        await sqliteService.updateSetting(SQLiteService.colTheme, 'dark');
        final settings = await sqliteService.getUserSettings();
        expect(settings[SQLiteService.colTheme], 'dark');
      });

      test('Update and Get User Setting - Language', () async {
        await sqliteService.updateSetting(SQLiteService.colLanguage, 'es');
        final settings = await sqliteService.getUserSettings();
        expect(settings[SQLiteService.colLanguage], 'es');
      });

      test('Update and Get User Setting - HasConsented', () async {
        await sqliteService.updateSetting(SQLiteService.colHasConsented, 1);
        final settings = await sqliteService.getUserSettings();
        expect(settings[SQLiteService.colHasConsented], 1);
      });

      test('Update and Get User Setting - PreferredCategories', () async {
        final categories = jsonEncode(['sports', 'finance']);
        await sqliteService.updateSetting(SQLiteService.colPreferredCategories, categories);
        final settings = await sqliteService.getUserSettings();
        expect(settings[SQLiteService.colPreferredCategories], categories);
      });

      test('Update and Get User Setting - LastArticlesSyncTimestamp', () async {
        final nowStr = DateTime.now().toIso8601String();
        await sqliteService.updateSetting(SQLiteService.colLastArticlesSyncTimestamp, nowStr);
        final settings = await sqliteService.getUserSettings();
        expect(settings[SQLiteService.colLastArticlesSyncTimestamp], nowStr);
      });

      test('Update and Get User Setting - LastFiltersSyncTimestamp', () async {
        final nowStr = DateTime.now().toIso8601String();
        await sqliteService.updateSetting(SQLiteService.colLastFiltersSyncTimestamp, nowStr);
        final settings = await sqliteService.getUserSettings();
        expect(settings[SQLiteService.colLastFiltersSyncTimestamp], nowStr);
      });

      test('Multiple updates ensure single row', () async {
        await sqliteService.updateSetting(SQLiteService.colTheme, 'light');
        await sqliteService.updateSetting(SQLiteService.colLanguage, 'fr');

        final settings = await sqliteService.getUserSettings();
        expect(settings[SQLiteService.colTheme], 'light');
        expect(settings[SQLiteService.colLanguage], 'fr');

        // This is implicitly tested as there's only one settings row with id=1.
        // A direct count query could be added if more explicit proof is needed,
        // but the updateSetting logic (insert with ignore then update) handles this.
        final db = await sqliteService.database;
        final result = await db.query(SQLiteService.userSettingsTable);
        expect(result.length, 1, reason: "User settings should always have only one row.");
      });
    });
  });
}
