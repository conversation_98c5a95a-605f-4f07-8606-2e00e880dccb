import 'dart:async';
import 'package:flutter/foundation.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:news4humans/repositories/article_repository.dart'; // Added
import 'package:news4humans/repositories/filter_repository.dart';   // Added
import '../models/article.dart';
import '../models/filter.dart';
import '../services/news_api_service.dart';
import '../services/firestore_service.dart'; // Kept for now for likedArticles, but will be removed from some methods
import '../services/mcp_service.dart';


class NewsProvider with ChangeNotifier {
  final NewsApiService _newsApiService;
  final FirestoreService _firestoreService; // Kept for now
  final McpService _mcpService;
  final ArticleRepository _articleRepository; // Added
  final FilterRepository _filterRepository;   // Added

  List<Article> _articles = [];
  List<Filter> _filters = [];
  Filter? _selectedFilter;
  bool _isLoading = false;
  String? _error;
  bool _hasMoreArticles = true;
  int _currentPage = 1;
  String? _currentQuery;
  String? _selectedCategory;
  String _searchQuery = '';

  NewsProvider({
    required NewsApiService newsApiService,
    required FirestoreService firestoreService, // Kept for now
    required McpService mcpService,
    required ArticleRepository articleRepository, // Added
    required FilterRepository filterRepository,   // Added
  })  : _newsApiService = newsApiService,
        _firestoreService = firestoreService,
        _mcpService = mcpService,
        _articleRepository = articleRepository, // Added
        _filterRepository = filterRepository { // Added
    // Cargar artículos iniciales y filtros locales
    loadFilters(); // Changed from loadFilters(userId)
    getArticlesWithCurrentFilters();
  }

  List<Article> get articles => _articles;
  List<Filter> get filters => _filters;
  Filter? get selectedFilter => _selectedFilter;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasMoreArticles => _hasMoreArticles;
  String? get selectedCategory => _selectedCategory;
  String get searchQuery => _searchQuery;

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setError(String? value) {
    _error = value;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  // Load local filters
  Future<void> loadFilters() async { // Removed userId parameter
    _setLoading(true);
    _clearError();
    try {
      _filters = await _filterRepository.getAllFilters();
      if (_filters.isEmpty) {
        // Optionally create default filters if none exist
        print("No local filters found. Consider creating default ones.");
        // Example: await addFilter(Filter.topHeadlines(id: 'default-tech', name: 'Tech', category: 'technology'));
      }
      if (_filters.isNotEmpty && _selectedFilter == null) {
        // If no filter was previously selected, select the first one.
        // Avoid re-fetching if a filter is already selected and articles are loaded.
        // _selectedFilter = _filters.first; // This might trigger an immediate fetch.
                                          // Let's defer selection or handle it more gracefully.
      }
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Add a new filter locally
  Future<void> addFilter(Filter filter) async { // Removed userId parameter
    _setLoading(true);
    _clearError();
    try {
      await _filterRepository.saveFilter(filter);
      // Refresh list from source to ensure consistency (e.g. if ID is auto-generated or filter is modified by save)
      await loadFilters(); // Reloads all filters
      // Or, if saveFilter returns the saved filter (especially if ID is generated):
      // _filters = [..._filters, savedFilterFromRepo];
      // notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Select a filter and fetch articles
  Future<void> selectFilter(Filter filter) async {
    _setLoading(true);
    _clearError();

    try {
      _selectedFilter = filter;
      _currentPage = 1;
      _hasMoreArticles = true;
      _articles = [];

      await getArticlesWithCurrentFilters();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Clear selected filter
  void clearFilter() {
    _selectedFilter = null;
    _currentPage = 1;
    _hasMoreArticles = true;
    _articles = [];
    notifyListeners();
  }

  // Fetch articles based on current filter
  Future<void> fetchArticles({
    String? query,
    bool refresh = false,
  }) async {
    if (_isLoading) return;

    if (refresh) {
      _articles = [];
      _currentPage = 1;
      _hasMoreArticles = true;
      _error = null;
    }

    if (!_hasMoreArticles && !refresh) return;

    _isLoading = true;
    notifyListeners();

    try {
      print(
          "⚡ Intentando obtener artículos con query: $query, página: $_currentPage");

      // Intentar obtener artículos a través del servicio
      List<Article> newArticles = [];
      // try { // Original API call block
      newArticles = await _newsApiService.getArticles(
        query: query ?? _selectedFilter?.parameters['q'] as String?,
        page: _currentPage,
      );
      print("📰 Artículos obtenidos de API: ${newArticles.length}");
      // } catch (apiError) { // Original API error handling
      //   print("❗ Error al obtener artículos de API: $apiError");
      //   // If API fails, consider fetching from local cache (ArticleRepository.getAllArticles())
      //   // This part needs careful thought: do we mix API errors with cache fallback here?
      //   // For now, let's stick to the original behavior of just catching and potentially having an empty newArticles list.
      // }

      if (newArticles.isEmpty) {
        _hasMoreArticles = false;
        print("❌ No hay más artículos disponibles");
      } else {
        // Save fetched articles to local DB
        for (final article in newArticles) {
          // Ensure `isSaved` is false unless explicitly set by user actions later
          // The article from API won't have local `isSaved` or `savedAt` status.
          // The copyWith here is important if the Article from API has these fields as non-nullable.
          // Assuming Article model handles defaults for isSaved, savedAt, updatedAt.
          await _articleRepository.saveArticle(article);
        }
        _articles.addAll(newArticles);
        _currentPage++;
        print("✅ Total de artículos cargados: ${_articles.length}");
      }

      _currentQuery = query;
      _error = null;
    } catch (e) {
      _error = e.toString();
      print("🔴 Error al obtener artículos: $_error");
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Delete a filter
  Future<void> deleteFilter(String filterId) async { // No userId needed if local
    _setLoading(true);
    _clearError();
    try {
      await _filterRepository.deleteFilter(filterId);
      _filters.removeWhere((f) => f.id == filterId);
      if (_selectedFilter?.id == filterId) {
        _selectedFilter = _filters.isNotEmpty ? _filters.first : null;
        // If a filter was active, and it's deleted, fetch articles based on new selection or clear.
        if (_selectedFilter != null) {
          await selectFilter(_selectedFilter!); // This will re-fetch articles
        } else {
          _articles = []; // Clear articles if no filter is selected
          _hasMoreArticles = true; // Reset pagination
          _currentPage = 1;
        }
      }
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Generate AI filter based on user input
  Future<Filter?> generateAiFilter(String userInput) async { // Removed userId
    _setLoading(true);
    _clearError();
    try {
      final filterData = await _mcpService.generateNewsFilter(userInput);
      final newFilter = Filter.aiGenerated(
        id: 'ai-${DateTime.now().millisecondsSinceEpoch}', // Consider more robust ID generation if syncing
        name: filterData['name'] ?? 'Custom AI Filter',
        description: filterData['description'] ?? 'AI generated based on your input',
        parameters: filterData['parameters'] ?? {},
      );
      await addFilter(newFilter); // Uses the refactored addFilter
      return newFilter;
    } catch (e) {
      _setError(e.toString());
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Save an article for later reading (mark as saved locally)
  Future<void> saveArticleForLater(Article article) async { // Renamed, removed userId
    try {
      // Ensure any existing local version is updated with isSaved = true
      // and a fresh savedAt timestamp.
      // The ArticleRepository.saveArticle has logic to handle isSaved and savedAt based on its params.
      // We call copyWith to ensure the isSaved flag is set correctly.
      await _articleRepository.saveArticle(
        article.copyWith(isSaved: true, savedAt: DateTime.now())
      );
      // Optionally, update the in-memory list if this article is present
      final index = _articles.indexWhere((a) => a.url == article.url);
      if (index != -1) {
        _articles[index] = _articles[index].copyWith(isSaved: true, savedAt: DateTime.now());
      }
      notifyListeners(); // Notify if local list changes or if UI depends on this state
    } catch (e) {
      _setError(e.toString());
      notifyListeners();
    }
  }

  // Get locally saved articles
  Future<List<Article>> getLocalSavedArticles() async { // Renamed, removed userId
    try {
      _clearError();
      return await _articleRepository.getSavedArticles();
    } catch (e) {
      _setError(e.toString());
      return [];
    }
  }

  // Unsave an article (mark as not saved locally)
  Future<void> unsaveArticle(String articleUrl) async { // Renamed, removed userId
    try {
      await _articleRepository.updateArticleSavedStatus(articleUrl, false, null);
      // Optionally, update the in-memory list
      final index = _articles.indexWhere((a) => a.url == articleUrl);
      if (index != -1) {
        _articles[index] = _articles[index].copyWith(isSaved: false, savedAt: null);
      }
      notifyListeners();  // Notify if local list changes or if UI depends on this state
    } catch (e) {
      _setError(e.toString());
      notifyListeners();
    }
  }

  // Update liked articles (still uses FirestoreService as per current scope)
  Future<bool> updateLikedArticles({
    required String userId,
    required List<String> likedArticles,
  }) async {
    try {
      await _firestoreService.updateLikedArticles(
        userId: userId,
        likedArticles: likedArticles,
      );
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  Future<String> generateSummary(Article article) async {
    try {
      return await _mcpService.generateSummary(article.content ?? '');
    } catch (e) {
      _error = e.toString();
      return 'Failed to generate summary';
    }
  }

  Future<String> generateAnalysis(Article article) async {
    try {
      return await _mcpService.generateAnalysis(article.content ?? '');
    } catch (e) {
      _error = e.toString();
      return 'Failed to generate analysis';
    }
  }

  // Establecer categoría y actualizar artículos
  Future<void> setCategory(String? category) async {
    if (_selectedCategory == category) return;

    _selectedCategory = category;
    notifyListeners();

    await getArticlesWithCurrentFilters();
  }

  // Establecer consulta de búsqueda y actualizar artículos
  Future<void> setSearchQuery(String query) async {
    if (_searchQuery == query) return;

    _searchQuery = query;
    notifyListeners();

    await getArticlesWithCurrentFilters();
  }

  // Obtener artículos de la API con los filtros actuales
  Future<void> getArticlesWithCurrentFilters() async {
    _isLoading = true;
    notifyListeners();

    try {
      final fetchedArticles = await _newsApiService.getArticles(
        query: _searchQuery.isEmpty ? null : _searchQuery,
        category: _selectedCategory,
      );

      _articles = []; // Clear before adding new ones
      for (final article in fetchedArticles) {
        await _articleRepository.saveArticle(article);
      }
      _articles = fetchedArticles;

    } catch (e) {
      print('Error fetching articles: $e');
      // Consider loading from local DB as fallback
      // _articles = await _articleRepository.getAllArticles();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refrescar artículos (para pull-to-refresh)
  Future<void> refreshArticles() async {
    try {
      // When refreshing, we fetch from API and then save to local DB.
      // The getArticlesWithCurrentFilters already does this, so refresh can call it.
      // Or, implement specific refresh logic here if it differs (e.g. different API endpoint).
      _currentPage = 1; // Reset current page for refresh
      _hasMoreArticles = true; // Assume there might be more
      _articles = []; // Clear current articles before fetching new ones
      await getArticlesWithCurrentFilters(); // This will fetch and save to DB
      // No, getArticlesWithCurrentFilters doesn't handle pagination or addAll, it replaces _articles.
      // fetchArticles is the one with pagination. Let's align refresh with fetchArticles's structure.

      // Re-implementing refresh logic similar to fetchArticles but ensuring it clears first.
      _setLoading(true);
      try {
        final refreshedArticles = await _newsApiService.getArticles(
          query: _searchQuery.isEmpty ? null : _searchQuery,
          category: _selectedCategory,
          page: 1, // Fetch page 1 for refresh
          refresh: true,
        );

        _articles = []; // Clear existing before adding
        for (final article in refreshedArticles) {
          await _articleRepository.saveArticle(article);
        }
        _articles = refreshedArticles;
        _currentPage = 2; // Next page to fetch would be 2
        _hasMoreArticles = refreshedArticles.isNotEmpty;

      } catch (e) {
        print('Error refreshing articles: $e');
        _setError(e.toString());
      } finally {
        _setLoading(false);
      }
    } catch (e) {
      print('Error refreshing articles: $e');
    }
  }
}
