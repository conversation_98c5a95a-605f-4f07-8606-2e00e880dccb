import 'package:news4humans/models/article.dart';
import 'package:news4humans/services/sqlite_service.dart';
import 'package:news4humans/repositories/settings_repository.dart'; // Import SettingsRepository
// Later, might import an API service:
// import 'package:news4humans/services/news_api_service.dart'; // TODO: Add NewsApiService dependency

class ArticleRepository {
  final SQLiteService _sqliteService;
  final SettingsRepository _settingsRepository;
  // final NewsApiService _newsApiService; // TODO: Add NewsApiService dependency for actual network calls

  ArticleRepository({
    required SQLiteService sqliteService,
    required SettingsRepository settingsRepository,
    // required NewsApiService newsApiService,
  })  : _sqliteService = sqliteService,
        _settingsRepository = settingsRepository;
        // _newsApiService = newsApiService;

  Future<void> saveArticle(Article article, {bool isSaved = false, DateTime? savedAt}) async {
    // Note: The isSaved and savedAt parameters might be redundant if the Article object
    // is already configured correctly before being passed to this method.
    // The SQLiteService.saveArticle was already designed to use the article's state.
    // If these params are to OVERRIDE the article's state, logic would be needed here
    // to create a new Article object or modify it before passing to _sqliteService.
    // For now, assuming 'article' is the source of truth for these fields.

    // We could create a new Article instance if isSaved or savedAt are provided and different
    // from the article's current state.
    Article articleToSave = article;
    if (isSaved && (article.isSaved != isSaved || savedAt != null)) {
        articleToSave = article.copyWith(
            isSaved: isSaved,
            savedAt: savedAt ?? DateTime.now()
        );
    } else if (!isSaved && article.isSaved) {
        // If un-saving, clear savedAt
        articleToSave = article.copyWith(isSaved: false, savedAt: null);
    }

    return _sqliteService.saveArticle(articleToSave);
  }

  Future<Article?> getArticle(String url) {
    return _sqliteService.getArticle(url);
  }

  Future<List<Article>> getAllArticles() {
    return _sqliteService.getAllArticles();
  }

  Future<List<Article>> getSavedArticles() {
    return _sqliteService.getSavedArticles();
  }

  Future<void> updateArticleSavedStatus(String url, bool isSaved, DateTime? savedAt) {
    // This method directly updates status, so it's not just passing an Article object.
    return _sqliteService.updateArticleSavedStatus(url, isSaved, savedAt);
  }

  Future<void> deleteArticle(String url) {
    return _sqliteService.deleteArticle(url);
  }

  Future<void> deleteAllArticles() {
    return _sqliteService.deleteAllArticles();
  }

  // Example of a method that might combine local and network data later:
  // Future<List<Article>> fetchArticles(String category) async {
  //   try {
  //     // Fetch from API
  //     List<Article> articles = await _newsApiService.fetchArticles(category);
  //     // Save to local DB (SQLiteService)
  //     for (var article in articles) {
  //       await _sqliteService.saveArticle(article);
  //     }
  //     return articles;
  //   } catch (e) {
  //     // If API fails, try to load from cache or throw error
  //     return _sqliteService.getAllArticles(); // Or articles by category
  //   }
  // }

  /// Fetches articles from the network.
  /// TODO: Implement actual network call to a news API.
  Future<List<Article>> fetchArticlesFromNetwork({Map<String, dynamic>? params}) async {
    print('ArticleRepository: fetchArticlesFromNetwork called with params: $params');
    // Placeholder: Replace with actual API call using _newsApiService
    // Example: return await _newsApiService.fetchArticles(params: params);
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay
    return Future.value([]); // Return empty list for now
    // throw UnimplementedError('fetchArticlesFromNetwork is not yet implemented.');
  }

  /// Synchronizes articles between the local database and the network.
  ///
  /// Future conflict resolution strategies:
  /// - Last Write Wins (LWW): Based on an `updatedAt` timestamp. If server's version is newer, take server's. If local is newer, potentially push to server (if supported).
  /// - Server Authoritative: Server data always overwrites local data on conflict.
  /// - Local Authoritative: Local data always overwrites server data (less common for fetched data).
  /// - Merge: For some data types, a field-level merge might be possible.
  /// - User Prompt: In some cases, prompt the user to resolve a conflict.
  ///
  /// Error Handling:
  /// - Network errors: Retry mechanisms (e.g., exponential backoff), or notify user.
  /// - Data parsing errors: Log error, potentially skip problematic items, notify user/devs.
  /// - Storage errors: Handle gracefully, notify user.
  Future<void> syncArticles() async {
    print('ArticleRepository: syncArticles called.');
    try {
      // 1. Get last sync timestamp for articles
      final DateTime? lastSyncTime = await _settingsRepository.getLastSyncTimestamp('articles');
      print('ArticleRepository: Last sync time for articles: $lastSyncTime');

      // 2. Fetch new/updated articles from network
      //    (Potentially pass lastSyncTime or pagination info to the API call)
      final List<Article> fetchedArticles = await fetchArticlesFromNetwork(
        params: {'since': lastSyncTime?.toIso8601String()},
      );

      if (fetchedArticles.isEmpty) {
        print('ArticleRepository: No new articles fetched from network.');
        // Optionally update sync timestamp even if no new articles, to indicate a check was made.
        // await _settingsRepository.updateLastSyncTimestamp('articles', DateTime.now());
        // return;
      } else {
        print('ArticleRepository: Fetched ${fetchedArticles.length} articles from network.');
      }

      // 3. Process fetched articles (Conceptual - full implementation is complex)
      for (final fetchedArticle in fetchedArticles) {
        final existingArticle = await _sqliteService.getArticle(fetchedArticle.url);
        if (existingArticle != null) {
          // Article exists locally, check if needs update (e.g., based on a timestamp or content hash)
          // For now, let's assume server is authoritative or use a simple updatedAt check.
          // if (fetchedArticle.updatedAt != null && existingArticle.updatedAt != null && fetchedArticle.updatedAt!.isAfter(existingArticle.updatedAt!)) {
          // Or simply replace if server is authoritative for content
             await _sqliteService.saveArticle(fetchedArticle.copyWith(updatedAt: DateTime.now()));
          // }
        } else {
          // New article, save it
          await _sqliteService.saveArticle(fetchedArticle.copyWith(updatedAt: DateTime.now()));
        }
      }

      // 4. Update last sync timestamp
      await _settingsRepository.updateLastSyncTimestamp('articles', DateTime.now());
      print('ArticleRepository: Articles sync timestamp updated.');

    } catch (e) {
      print('ArticleRepository: Error during syncArticles: $e');
      // Rethrow or handle as appropriate for the application
      // throw;
    }
    // For now, as it's a placeholder:
    // throw UnimplementedError('syncArticles is not fully implemented.');
  }
}
