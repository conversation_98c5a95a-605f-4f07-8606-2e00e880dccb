import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart'; // Using hive_flutter for initFlutter if service uses it
import 'package:news4humans/services/hive_service.dart';
import 'dart:io';

void main() {
  late HiveService hiveService;
  late Directory tempDir;

  setUpAll(() async {
    // Hive.initFlutter() needs to be called if the service or app uses Hive for Flutter features.
    // For pure Dart tests, Hive.init() is sufficient. Given the service uses hive_flutter,
    // it's safer to use a path that Hive.initFlutter() can work with.
    // However, Hive.initFlutter() itself might have platform channel dependencies not suitable for pure unit tests.
    // Let's try with Hive.init() and a temp path first. If HiveService.initHive() fails due to
    // missing Flutter bindings, we might need a more complex setup or to adjust HiveService.
    tempDir = await Directory.systemTemp.createTemp('hive_test_');
    Hive.init(tempDir.path);
    // No need to call Hive.initFlutter() here for unit tests if we just use Hive.openBox
  });

  setUp(() async {
    hiveService = HiveService();
    // initHive in the service calls Hive.initFlutter() and opens boxes.
    // For unit tests, this might be problematic if it relies on Flutter specific paths.
    // Let's see if the current HiveService.initHive() works with the Hive.init(tempDir.path) above.
    // If HiveService.initHive() internally calls Hive.initFlutter() without a path argument,
    // it might try to use default Flutter paths, ignoring our tempDir.
    // The HiveService.initHive() calls Hive.initFlutter() then Hive.openBox().
    // This should be okay as long as Hive.isInitialized is true after our Hive.init(tempDir.path).

    // Ensure boxes are opened for each test if initHive doesn't recreate or if tests modify them extensively.
    // The HiveService.initHive() opens generalAppCacheBox and userSettingsBox.
    // We will rely on it to open them. If tests need pristine boxes, they should clear them in setUp or tearDown.
    await hiveService.initHive(); // This will open the boxes defined in HiveService
  });

  tearDown(() async {
    await Hive.close(); // Close all boxes
    // It's good practice to delete data from boxes if tests don't run in true isolation for boxes
    // For example, clear the specific boxes used if not using deleteFromDisk.
    final generalBox = await Hive.openBox(HiveService.generalAppCacheBox);
    await generalBox.clear();
    final settingsBox = await Hive.openBox(HiveService.userSettingsBox);
    await settingsBox.clear();
    // Hive.deleteFromDisk(); // This deletes all boxes from the initialized path.
    // This can be problematic if other tests are running in parallel or if the temp path isn't unique per test suite.
    // Clearing boxes used by this test suite is safer.
  });

  // After all tests, delete the temporary directory
  tearDownAll(() async {
    if (await tempDir.exists()) {
      await tempDir.delete(recursive: true);
    }
  });

  group('HiveService General Cache Tests', () {
    const String testBox = HiveService.generalAppCacheBox;
    const String testKey = 'test_key';
    const String testValue = 'test_value';

    test('putData and getData correctly store and retrieve data', () async {
      await hiveService.putData(testBox, testKey, testValue);
      final retrievedValue = await hiveService.getData(testBox, testKey);
      expect(retrievedValue, testValue);
    });

    test('getData returns null for non-existent key', () async {
      final retrievedValue = await hiveService.getData(testBox, 'non_existent_key');
      expect(retrievedValue, isNull);
    });

    test('deleteData removes data correctly', () async {
      await hiveService.putData(testBox, testKey, testValue);
      var retrievedValue = await hiveService.getData(testBox, testKey);
      expect(retrievedValue, testValue);

      await hiveService.deleteData(testBox, testKey);
      retrievedValue = await hiveService.getData(testBox, testKey);
      expect(retrievedValue, isNull);
    });

    test('clearBox removes all data from the specified box', () async {
      await hiveService.putData(testBox, 'key1', 'value1');
      await hiveService.putData(testBox, 'key2', 'value2');

      await hiveService.clearBox(testBox);
      final value1 = await hiveService.getData(testBox, 'key1');
      final value2 = await hiveService.getData(testBox, 'key2');
      expect(value1, isNull);
      expect(value2, isNull);
    });
  });

  group('HiveService User Settings Cache Tests', () {
    final Map<String, dynamic> testSettings = {
      'theme': 'dark',
      'language': 'en',
    };

    test('cacheUserSettings and getCachedUserSettings correctly store and retrieve settings', () async {
      await hiveService.cacheUserSettings(testSettings);
      final retrievedSettings = await hiveService.getCachedUserSettings();
      expect(retrievedSettings, isNotNull);
      expect(retrievedSettings!['theme'], testSettings['theme']);
      expect(retrievedSettings['language'], testSettings['language']);
    });

    test('getCachedUserSettings returns null if no settings are cached', () async {
      // Ensure cache is clear for this specific key (it should be due to tearDown/setUp logic)
      await hiveService.clearUserSettingsCache();
      final retrievedSettings = await hiveService.getCachedUserSettings();
      expect(retrievedSettings, isNull);
    });

    test('clearUserSettingsCache removes the user settings', () async {
      await hiveService.cacheUserSettings(testSettings);
      var retrievedSettings = await hiveService.getCachedUserSettings();
      expect(retrievedSettings, isNotNull);

      await hiveService.clearUserSettingsCache();
      retrievedSettings = await hiveService.getCachedUserSettings();
      expect(retrievedSettings, isNull);
    });

     test('putData with complex map into general box', () async {
      const String box = HiveService.generalAppCacheBox;
      const String key = 'complex_map_key';
      final Map<String, dynamic> complexValue = {
        'name': 'Test User',
        'age': 30,
        'isVerified': true,
        'scores': [10, 20, 30],
        'nested': {'a': 1, 'b': 'hello'}
      };
      await hiveService.putData(box, key, complexValue);
      final retrievedValue = await hiveService.getData(box, key);
      expect(retrievedValue, isA<Map<String, dynamic>>());
      expect(retrievedValue, complexValue);
    });
  });
}
