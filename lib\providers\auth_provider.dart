import 'dart:convert'; // For jsonEncode
import 'package:flutter/foundation.dart';
import 'package:news4humans/repositories/article_repository.dart'; // Added
import 'package:news4humans/repositories/settings_repository.dart'; // Added
import '../models/user.dart';
import '../services/firebase_auth_service.dart';
import '../services/firestore_service.dart';
import '../services/local_storage_service.dart';


class AuthProvider extends ChangeNotifier {
  final FirebaseAuthService _authService;
  final FirestoreService _firestoreService;
  final LocalStorageService _localStorageService;
  final ArticleRepository _articleRepository;   // Added
  final SettingsRepository _settingsRepository; // Added

  UserModel? _currentUser;
  bool _isLoading = false;
  String? _error;

  AuthProvider({
    required FirebaseAuthService authService,
    required FirestoreService firestoreService,
    required LocalStorageService localStorageService,
    required ArticleRepository articleRepository,   // Added
    required SettingsRepository settingsRepository, // Added
  })  : _authService = authService,
        _firestoreService = firestoreService,
        _localStorageService = localStorageService,
        _articleRepository = articleRepository,     // Added
        _settingsRepository = settingsRepository;   // Added

  UserModel? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser != null;
  bool get isLoading => _isLoading;
  String? get error => _error;

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setError(String? value) {
    _error = value;
    notifyListeners();
  }

  Future<void> initialize() async {
    final user = await _localStorageService.getUser();
    if (user != null) {
      _currentUser = user;
      notifyListeners();
    }
  }

  Future<bool> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final user = await _authService.signUp(
        email: email,
        password: password,
        name: name,
      );

      _currentUser = user;
      await _localStorageService.saveUser(user);
      if (_currentUser != null) {
        await _settingsRepository.updateSetting('hasConsentedToDataCollection', _currentUser!.hasConsentedToDataCollection ? 1 : 0);
        await _settingsRepository.updateSetting('preferredCategories', jsonEncode(_currentUser!.preferredCategories));
      }
      if (_currentUser != null) {
        await _settingsRepository.updateSetting('hasConsentedToDataCollection', _currentUser!.hasConsentedToDataCollection ? 1 : 0);
        await _settingsRepository.updateSetting('preferredCategories', jsonEncode(_currentUser!.preferredCategories));
      }
      if (_currentUser != null) {
        await _settingsRepository.updateSetting('hasConsentedToDataCollection', _currentUser!.hasConsentedToDataCollection ? 1 : 0);
        await _settingsRepository.updateSetting('preferredCategories', jsonEncode(_currentUser!.preferredCategories));
      }
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final user = await _authService.signIn(email: email, password: password);
      _currentUser = user;
      await _localStorageService.saveUser(user);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signInWithGoogle() async {
    try {
      _setLoading(true);
      _setError(null);

      final user = await _authService.signInWithGoogle();
      _currentUser = user;
      await _localStorageService.saveUser(user);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    try {
      await _authService.signOut();
      await _localStorageService.deleteUser();
      _currentUser = null;
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to sign out: $e');
    }
  }

  Future<void> updateProfile({
    String? name,
    String? email,
    String? avatarUrl,
  }) async {
    try {
      if (_currentUser == null) return;

      final updatedUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        email: email ?? _currentUser!.email,
        avatarUrl: avatarUrl ?? _currentUser!.avatarUrl,
      );

      await _firestoreService.updateUserProfile(
        _currentUser!.id,
        {
          'name': updatedUser.name,
          'email': updatedUser.email,
          'avatarUrl': updatedUser.avatarUrl,
        },
      );

      _currentUser = updatedUser;
      await _localStorageService.saveUser(updatedUser);
      if (_currentUser != null) {
        await _settingsRepository.updateSetting('hasConsentedToDataCollection', _currentUser!.hasConsentedToDataCollection ? 1 : 0);
      }

      // Sync with local ArticleRepository for isSaved status
      final bool isLiked = likedArticles.contains(articleUrl);
      await _articleRepository.updateArticleSavedStatus(articleUrl, isLiked, isLiked ? DateTime.now() : null);

      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<void> updateUserConsent({required bool hasConsented}) async {
    try {
      if (_currentUser == null) return;

      final updatedUser = _currentUser!.copyWith(
        hasConsentedToDataCollection: hasConsented,
      );

      await _firestoreService.updateUserProfile(
        _currentUser!.id,
        {'hasConsentedToDataCollection': hasConsented},
      );

      _currentUser = updatedUser;
      await _localStorageService.saveUser(updatedUser);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<void> toggleArticleLike(String articleUrl) async {
    try {
      if (_currentUser == null) return;

      final likedArticles = [..._currentUser!.likedArticles];
      if (likedArticles.contains(articleUrl)) {
        likedArticles.remove(articleUrl);
      } else {
        likedArticles.add(articleUrl);
      }

      final updatedUser = _currentUser!.copyWith(
        likedArticles: likedArticles,
      );

      await _firestoreService.updateUserProfile(
        _currentUser!.id,
        {'likedArticles': likedArticles},
      );

      _currentUser = updatedUser;
      await _localStorageService.saveUser(updatedUser);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<void> updateProStatus(bool isPro) async {
    try {
      if (_currentUser == null) return;

      final updatedUser = _currentUser!.copyWith(
        isPro: isPro,
      );

      await _firestoreService.updateUserProfile(
        _currentUser!.id,
        {'isPro': isPro},
      );

      _currentUser = updatedUser;
      await _localStorageService.saveUser(updatedUser);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Alias methods for backward compatibility
  Future<void> likeArticle(String articleUrl) => toggleArticleLike(articleUrl);
  Future<void> unlikeArticle(String articleUrl) =>
      toggleArticleLike(articleUrl);
}
