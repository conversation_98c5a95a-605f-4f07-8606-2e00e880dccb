import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:news4humans/models/filter.dart';
import 'package:news4humans/repositories/filter_repository.dart';
import 'package:news4humans/services/sqlite_service.dart';
import '../mocks/mocks.dart'; // Your generated or manual mocks
import 'dart:convert'; // For jsonEncode in helper if needed for parameters

// Helper method to create a sample Filter (can be moved to a shared test utility file later)
Filter createSampleFilter({
  String id = 'filter1',
  String name = 'Sample Filter 1',
  String description = 'This is a sample filter description.',
  Map<String, dynamic>? parameters,
  bool isAiGenerated = false,
  DateTime? createdAt,
  bool isEnabled = true,
  DateTime? updatedAt,
}) {
  return Filter(
    id: id,
    name: name,
    description: description,
    parameters: parameters ?? {'keyword': 'sample'},
    isAiGenerated: isAiGenerated,
    createdAt: createdAt ?? DateTime.now().subtract(const Duration(days: 1)),
    isEnabled: isEnabled,
    updatedAt: updatedAt ?? DateTime.now(),
  );
}


void main() {
  late MockSQLiteService mockSQLiteService;
  late FilterRepository filterRepository;

  setUp(() {
    mockSQLiteService = MockSQLiteService();
    filterRepository = FilterRepository(sqliteService: mockSQLiteService);

    // Default stubs for methods returning Futures to avoid MissingStubError
    when(mockSQLiteService.saveFilter(any)).thenAnswer((_) async => Future.value());
    when(mockSQLiteService.getFilter(any)).thenAnswer((_) async => null);
    when(mockSQLiteService.getAllFilters()).thenAnswer((_) async => []);
    when(mockSQLiteService.deleteFilter(any)).thenAnswer((_) async => Future.value());
    when(mockSQLiteService.deleteAllFilters()).thenAnswer((_) async => Future.value());
  });

  group('FilterRepository Passthrough Method Tests', () {
    test('saveFilter calls SQLiteService.saveFilter', () async {
      final filter = createSampleFilter();
      await filterRepository.saveFilter(filter);
      verify(mockSQLiteService.saveFilter(filter)).called(1);
    });

    test('getFilter calls SQLiteService.getFilter and returns its value', () async {
      final filter = createSampleFilter(id: 'test/get');
      when(mockSQLiteService.getFilter('test/get')).thenAnswer((_) async => filter);

      final result = await filterRepository.getFilter('test/get');

      expect(result, filter);
      verify(mockSQLiteService.getFilter('test/get')).called(1);
    });

    test('getAllFilters calls SQLiteService.getAllFilters and returns its value', () async {
      final filters = [createSampleFilter(id: 'test/all1'), createSampleFilter(id: 'test/all2')];
      when(mockSQLiteService.getAllFilters()).thenAnswer((_) async => filters);

      final result = await filterRepository.getAllFilters();

      expect(result, filters);
      verify(mockSQLiteService.getAllFilters()).called(1);
    });

    test('deleteFilter calls SQLiteService.deleteFilter', () async {
      const id = 'test/delete';
      await filterRepository.deleteFilter(id);
      verify(mockSQLiteService.deleteFilter(id)).called(1);
    });

    test('deleteAllFilters calls SQLiteService.deleteAllFilters', () async {
      await filterRepository.deleteAllFilters();
      verify(mockSQLiteService.deleteAllFilters()).called(1);
    });
  });
}
