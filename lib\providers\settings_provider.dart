import 'dart:convert'; // For jsonEncode
import 'package:flutter/material.dart';
import 'package:news4humans/repositories/settings_repository.dart';
import 'package:news4humans/services/sqlite_service.dart'; // For settings keys

class SettingsProvider with ChangeNotifier {
  final SettingsRepository _settingsRepository;

  String _themeMode = 'system'; // Default theme
  String _language = 'en';     // Default language
  List<String> _preferredCategories = [];
  bool _hasConsented = false;

  SettingsProvider({required SettingsRepository settingsRepository})
      : _settingsRepository = settingsRepository {
    // Optionally load settings when provider is created,
    // or rely on an explicit call from UI/main.
    // loadSettings();
  }

  // Public getters
  String get themeMode => _themeMode;
  String get language => _language;
  List<String> get preferredCategories => List.unmodifiable(_preferredCategories);
  bool get hasConsented => _hasConsented;

  Future<void> loadSettings() async {
    try {
      final settings = await _settingsRepository.getSettings();

      _themeMode = settings[SQLiteService.colTheme] as String? ?? 'system';
      _language = settings[SQLiteService.colLanguage] as String? ?? 'en';

      final categoriesJson = settings[SQLiteService.colPreferredCategories] as String?;
      if (categoriesJson != null && categoriesJson.isNotEmpty) {
        try {
          final decodedCategories = jsonDecode(categoriesJson);
          if (decodedCategories is List) {
            _preferredCategories = decodedCategories.map((e) => e.toString()).toList();
          } else {
            _preferredCategories = [];
          }
        } catch (e) {
          print("Error decoding preferredCategories: $e");
          _preferredCategories = [];
        }
      } else {
        _preferredCategories = [];
      }

      _hasConsented = (settings[SQLiteService.colHasConsented] as int? ?? 0) == 1;

      notifyListeners();
    } catch (e) {
      print("Error loading settings: $e");
      // Handle error appropriately, maybe set defaults
    }
  }

  Future<void> updateThemeMode(String themeMode) async {
    try {
      await _settingsRepository.updateSetting(SQLiteService.colTheme, themeMode);
      _themeMode = themeMode;
      notifyListeners();
    } catch (e) {
      print("Error updating theme mode: $e");
      // Handle error
    }
  }

  Future<void> updateLanguage(String languageCode) async {
    try {
      await _settingsRepository.updateSetting(SQLiteService.colLanguage, languageCode);
      _language = languageCode;
      notifyListeners();
    } catch (e) {
      print("Error updating language: $e");
      // Handle error
    }
  }

  Future<void> updatePreferredCategories(List<String> categories) async {
    try {
      await _settingsRepository.updateSetting(SQLiteService.colPreferredCategories, jsonEncode(categories));
      _preferredCategories = List.from(categories); // Create a new list
      notifyListeners();
    } catch (e) {
      print("Error updating preferred categories: $e");
      // Handle error
    }
  }

  Future<void> updateConsent(bool consented) async {
    try {
      await _settingsRepository.updateSetting(SQLiteService.colHasConsented, consented ? 1 : 0);
      _hasConsented = consented;
      notifyListeners();
    } catch (e) {
      print("Error updating consent: $e");
      // Handle error
    }
  }
}
