# News4Humans 📰

Una aplicación de noticias moderna con IA que te permite personalizar tu experiencia de lectura, totalmente local y sin dependencia de backend.

## 🌟 Características Principales

- 🤖 **Filtros Inteligentes con IA**: Crea filtros personalizados usando IA para encontrar las noticias más relevantes
- 📱 **Interfaz Moderna**: Diseño limpio y minimalista siguiendo las últimas tendencias
- 🔍 **Múltiples Fuentes**: Integración con NewsAPI y soporte futuro para HyperBrowser MCP
- 👤 **Perfiles de Usuario**: Cuentas personalizadas con preferencias y artículos guardados
- 💾 **Almacenamiento Local**: Guarda artículos para leer sin conexión
- 🎯 **Personalización**: Filtros personalizados y recomendaciones basadas en tus intereses

## 📸 Capturas de Pantalla

### Feed Principal y Filtros
![Feed Principal](docs/img/Screenshot%202025-06-10%20025312.png)
![Filtros Personalizados](docs/img/Screenshot%202025-06-10%20025323.png)

### Detalles de Artículo y Guardado
![Detalle de Artículo](docs/img/Screenshot%202025-06-10%20025337.png)
![Artículos Guardados](docs/img/Screenshot%202025-06-10%20025344.png)

### Creación de Filtros con IA
![Creación de Filtros](docs/img/Screenshot%202025-06-10%20025352.png)
![Filtros IA](docs/img/Screenshot%202025-06-10%20025404.png)

## 🚀 Instalación

1. **Requisitos Previos**
   ```bash
   flutter --version   # Asegúrate de tener Flutter 3.2.6 o superior
   ```

2. **Clonar el Repositorio**
   ```bash
   git clone https://github.com/tu-usuario/news4humans.git
   cd news4humans
   ```

3. **Instalar Dependencias**
   ```bash
   flutter pub get
   ```

4. **Configurar Variables de Entorno**
   - Crea un archivo `.env` en la raíz del proyecto
   ```env
   NEWS_API_KEY=tu_api_key_aqui
   ```

5. **Ejecutar la Aplicación**
   ```bash
   flutter run
   ```

## 🛠️ Tecnologías Utilizadas

- **Flutter & Dart**: Framework y lenguaje principal
- **Provider**: Gestión de estado
- **SQLite/sqflite**: Base de datos local para almacenamiento de noticias y configuraciones
- **Hive**: Almacenamiento key-value para caché y configuraciones rápidas
- **NewsAPI**: Fuente principal de noticias (configurable)
- **Flutter Animate**: Animaciones UI
- **LLM Integration**: Soporte para modelos de lenguaje locales
- **Local AI Processing**: Procesamiento de IA en dispositivo

## 📱 Funcionalidades

### Procesamiento Local de IA
- Integración con modelos LLM en dispositivo
- Análisis y enriquecimiento de noticias sin backend
- Generación de resúmenes y insights
- Personalización basada en preferencias del usuario

### Filtros Inteligentes
- Creación de filtros personalizados usando IA
- Filtros por categoría (Tecnología, Negocios, Ciencia, etc.)
- Búsqueda avanzada y filtrado por fuente

### Gestión de Artículos
- Guardar artículos para lectura offline
- Compartir artículos
- Sistema de "Me gusta" y recomendaciones

### Personalización
- Temas personalizados
- Preferencias de categorías
- Historial de lectura

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add: Nueva característica'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📝 Licencia

Este proyecto está bajo la Licencia MIT Modificada - ver el archivo [LICENSE.md](LICENSE.md) para más detalles.

## 📧 Contacto

Don Beri - [@x_donberi](https://x.com/x_donberi)
GitHub: [iberi22](https://github.com/iberi22)

Link del Proyecto: [https://github.com/iberi22/news4humans](https://github.com/iberi22/news4humans)
