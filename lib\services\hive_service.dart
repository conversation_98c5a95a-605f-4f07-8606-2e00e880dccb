import 'package:hive_flutter/hive_flutter.dart';

class HiveService {
  static const String userSettingsBox = 'user_settings_cache';
  static const String generalAppCacheBox = 'general_app_cache'; // Renamed from appCacheBox

  // Initializes Hive and opens necessary boxes.
  Future<void> initHive() async {
    await Hive.initFlutter();
    await Hive.openBox(generalAppCacheBox);
    await Hive.openBox(userSettingsBox);
    // Register adapters here if needed in the future
    // Hive.registerAdapter(YourCustomAdapter());
  }

  // Puts data into the specified box.
  Future<void> putData(String boxName, String key, dynamic value) async {
    // Hive.openBox is idempotent, it will return the already opened box if it exists.
    final box = await Hive.openBox(boxName);
    await box.put(key, value);
  }

  // Gets data from the specified box.
  Future<dynamic> getData(String boxName, String key) async {
    final box = await Hive.openBox(boxName);
    return box.get(key);
  }

  // Deletes data from the specified box.
  Future<void> deleteData(String boxName, String key) async {
    final box = await Hive.openBox(boxName);
    await box.delete(key);
  }

  // Clears all data from the specified box.
  Future<void> clearBox(String boxName) async {
    final box = await Hive.openBox(boxName);
    await box.clear();
  }

  // Closes all open Hive boxes.
  Future<void> closeAllBoxes() async {
    await Hive.close();
  }

  // --- User Settings Specific Methods ---

  static const String _userSettingsKey = 'current_user_settings';

  // Caches the user settings map.
  Future<void> cacheUserSettings(Map<String, dynamic> settings) async {
    await putData(userSettingsBox, _userSettingsKey, settings);
  }

  // Retrieves the cached user settings map.
  Future<Map<String, dynamic>?> getCachedUserSettings() async {
    final dynamic settings = await getData(userSettingsBox, _userSettingsKey);
    if (settings != null && settings is Map) {
      // Ensure it's a Map<String, dynamic>
      return Map<String, dynamic>.from(settings);
    }
    return null;
  }

  // Clears the user settings from the cache.
  Future<void> clearUserSettingsCache() async {
    await deleteData(userSettingsBox, _userSettingsKey);
    // As an alternative, if the box should be completely emptied:
    // await clearBox(userSettingsBox);
    // However, deleting by key is safer if other settings might be added to this box later.
  }
}
