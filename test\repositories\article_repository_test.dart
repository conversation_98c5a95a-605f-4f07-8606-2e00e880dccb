import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:news4humans/models/article.dart';
import 'package:news4humans/repositories/article_repository.dart';
import 'package:news4humans/services/sqlite_service.dart';
import 'package:news4humans/repositories/settings_repository.dart';
import '../mocks/mocks.dart';

// Helper method to create a sample Article (can be moved to a shared test utility file later)
Article createSampleArticle({
  String url = 'http://example.com/article1',
  String title = 'Sample Article 1',
  String? description = 'This is a sample article description.',
  String? content = 'Full content of the sample article.',
  String? author = '<PERSON>',
  String? source = 'Sample News',
  String? category = 'Technology',
  String? urlToImage = 'http://example.com/image1.jpg',
  DateTime? publishedAt,
  int readTime = 5,
  bool isSaved = false,
  DateTime? savedAt,
  DateTime? updatedAt,
}) {
  return Article(
    url: url,
    title: title,
    description: description,
    content: content,
    author: author,
    source: source,
    category: category,
    urlToImage: urlToImage,
    publishedAt: publishedAt ?? DateTime.now().subtract(const Duration(days: 1)),
    readTime: readTime,
    isSaved: isSaved,
    savedAt: savedAt,
    updatedAt: updatedAt ?? DateTime.now(),
  );
}

void main() {
  late MockSQLiteService mockSQLiteService;
  late MockSettingsRepository mockSettingsRepository;
  late ArticleRepository articleRepository;

  setUp(() {
    mockSQLiteService = MockSQLiteService();
    mockSettingsRepository = MockSettingsRepository();
    articleRepository = ArticleRepository(
      sqliteService: mockSQLiteService,
      settingsRepository: mockSettingsRepository,
    );

    // Default stubs for methods returning Futures
    when(mockSQLiteService.saveArticle(any)).thenAnswer((_) async => Future.value());
    when(mockSQLiteService.getArticle(any)).thenAnswer((_) async => null);
    when(mockSQLiteService.getAllArticles()).thenAnswer((_) async => []);
    when(mockSQLiteService.getSavedArticles()).thenAnswer((_) async => []);
    when(mockSQLiteService.updateArticleSavedStatus(any, any, any)).thenAnswer((_) async => Future.value());
    when(mockSQLiteService.deleteArticle(any)).thenAnswer((_) async => Future.value());
    when(mockSQLiteService.deleteAllArticles()).thenAnswer((_) async => Future.value());

    when(mockSettingsRepository.getLastSyncTimestamp(any)).thenAnswer((_) async => null);
    when(mockSettingsRepository.updateLastSyncTimestamp(any, any)).thenAnswer((_) async => Future.value());
  });

  group('ArticleRepository Passthrough Method Tests', () {
    test('saveArticle calls SQLiteService.saveArticle', () async {
      final article = createSampleArticle();
      // The repository's saveArticle has logic to call copyWith, so we need to capture that.
      // We expect saveArticle in SQLiteService to be called with an Article.
      when(mockSQLiteService.saveArticle(any)).thenAnswer((_) async => Future.value());

      await articleRepository.saveArticle(article, isSaved: true, savedAt: DateTime.now());

      // Verify that saveArticle was called on the mock.
      // The `any` matcher can be used, or `argThat` for more specific argument matching.
      // The important part is that the SQLiteService's method was called.
      // Due to the copyWith logic, the exact instance won't match unless captured.
      final verification = verify(mockSQLiteService.saveArticle(captureAny));
      expect(verification.captured.single, isA<Article>());
      expect((verification.captured.single as Article).url, article.url);
      expect((verification.captured.single as Article).isSaved, true);

    });

    test('getArticle calls SQLiteService.getArticle and returns its value', () async {
      final article = createSampleArticle(url: 'test/get');
      when(mockSQLiteService.getArticle('test/get')).thenAnswer((_) async => article);

      final result = await articleRepository.getArticle('test/get');

      expect(result, article);
      verify(mockSQLiteService.getArticle('test/get')).called(1);
    });

    test('getAllArticles calls SQLiteService.getAllArticles and returns its value', () async {
      final articles = [createSampleArticle(url: 'test/all1'), createSampleArticle(url: 'test/all2')];
      when(mockSQLiteService.getAllArticles()).thenAnswer((_) async => articles);

      final result = await articleRepository.getAllArticles();

      expect(result, articles);
      verify(mockSQLiteService.getAllArticles()).called(1);
    });

    test('getSavedArticles calls SQLiteService.getSavedArticles and returns its value', () async {
      final savedArticles = [createSampleArticle(url: 'test/saved1', isSaved: true)];
      when(mockSQLiteService.getSavedArticles()).thenAnswer((_) async => savedArticles);

      final result = await articleRepository.getSavedArticles();

      expect(result, savedArticles);
      verify(mockSQLiteService.getSavedArticles()).called(1);
    });

    test('updateArticleSavedStatus calls SQLiteService.updateArticleSavedStatus', () async {
      const url = 'test/updateStatus';
      const isSaved = true;
      final savedAt = DateTime.now();

      await articleRepository.updateArticleSavedStatus(url, isSaved, savedAt);

      verify(mockSQLiteService.updateArticleSavedStatus(url, isSaved, savedAt)).called(1);
    });

    test('deleteArticle calls SQLiteService.deleteArticle', () async {
      const url = 'test/delete';
      await articleRepository.deleteArticle(url);
      verify(mockSQLiteService.deleteArticle(url)).called(1);
    });

    test('deleteAllArticles calls SQLiteService.deleteAllArticles', () async {
      await articleRepository.deleteAllArticles();
      verify(mockSQLiteService.deleteAllArticles()).called(1);
    });
  });

  group('ArticleRepository syncArticles Tests (Placeholder Logic)', () {
    test('syncArticles calls SettingsRepository and updates timestamp even with no fetched articles', () async {
      // Arrange: fetchArticlesFromNetwork (internal to ArticleRepository, but we know its placeholder behavior)
      // will return an empty list.
      final initialSyncTime = DateTime.now().subtract(const Duration(days: 1));
      when(mockSettingsRepository.getLastSyncTimestamp('articles')).thenAnswer((_) async => initialSyncTime);

      // Act
      await articleRepository.syncArticles();

      // Assert
      verify(mockSettingsRepository.getLastSyncTimestamp('articles')).called(1);
      // fetchArticlesFromNetwork is internal, its effect (empty list) is implicitly tested by...
      verifyNever(mockSQLiteService.saveArticle(any)); // No articles should be saved
      verify(mockSettingsRepository.updateLastSyncTimestamp('articles', any)).called(1);
    });

    // In a full implementation, you would add another test here:
    // test('syncArticles fetches, saves new articles, and updates existing ones', () async {
    //   // 1. Arrange:
    //   //    - Mock SettingsRepository.getLastSyncTimestamp
    //   //    - Mock (or make ArticleRepository use a mocked) NewsApiService.fetchArticles to return a list of new/updated articles.
    //   //    - Mock SQLiteService.getArticle for existing articles checks.
    //   //    - Mock SQLiteService.saveArticle.
    //
    //   // 2. Act:
    //   //    await articleRepository.syncArticles();
    //
    //   // 3. Assert:
    //   //    - Verify NewsApiService.fetchArticles was called (with correct params like 'since').
    //   //    - Verify SQLiteService.getArticle was called for each fetched article to check existence.
    //   //    - Verify SQLiteService.saveArticle was called for new articles and for updated articles.
    //   //    - Verify SettingsRepository.updateLastSyncTimestamp was called.
    // });
  });
}
