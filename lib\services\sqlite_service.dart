import 'dart:convert'; // For jsonEncode/jsonDecode
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:news4humans/models/article.dart';
import 'package:news4humans/models/filter.dart';
// Removed HiveService import and instantiation

class SQLiteService {
  static Database? _database;
  // final HiveService _hiveService = HiveService(); // Removed

  // static const String _dbName = "news4humans.db"; // Already defined below
  // static const int _dbVersion = 1; // Already defined below
  static const String _dbName = "news4humans.db";
  static const int _dbVersion = 1; // Increment for schema changes

  // User Settings Table
  static const String _userSettingsTable = "user_settings";
  static const String colTheme = "theme"; // Made public for SettingsRepository
  static const String colLanguage = "language"; // Made public
  static const String colHasConsented = "hasConsentedToDataCollection"; // Made public
  static const String colPreferredCategories = "preferredCategories"; // Made public
  // static const String colLastSyncTimestamp = "lastSyncTimestamp"; // This will be replaced by specific ones
  static const String colLastArticlesSyncTimestamp = "lastArticlesSyncTimestamp"; // New
  static const String colLastFiltersSyncTimestamp = "lastFiltersSyncTimestamp";   // New


  // Articles Table - column names from Article.toMap() keys
  static const String _articlesTable = "articles";
  static const String _colUrl = "url";
  // ... other article column names derived from Article.toMap()

  // Filters Table - column names from Filter.toMap() keys
  static const String _filtersTable = "filters";
  static const String _colFilterId = "id";
  // ... other filter column names derived from Filter.toMap()


  Future<Database> get database async {
    // If _database is null, or if initDB is called with a specific path (like for testing),
    // it should initialize. The static instance might cause issues if tests switch
    // between in-memory and file-based DBs without app restarts.
    // For simplicity in testing, we might re-initialize if a path is given to initDB.
    // However, the current structure with a static _database instance is more like a singleton.
    // Let's assume for now tests will manage their lifecycle via setUp/tearDown and re-instantiate SQLiteService.
    if (_database != null) return _database!;
    _database = await initDB(null); // Default init
    return _database!;
  }

  // Allows an optional dbPath for testing (e.g., in-memory)
  Future<Database> initDB(String? dbPath) async {
    String path;
    if (dbPath == null || dbPath.isEmpty) {
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      path = documentsDirectory.path + "/" + _dbName;
    } else if (dbPath == ':memory:') {
      path = dbPath; // Use in-memory path directly
    } else {
      path = dbPath; // Use custom path (e.g. for test-specific file DB)
    }

    // If a specific path is provided (often for testing or if switching DBs),
    // and if a database is already open, it might be for a different file/type.
    // Closing it ensures we open the correct one.
    // However, be cautious with a static _database instance.
    // For robust testing, it's better if SQLiteService is not a singleton,
    // or if tests can explicitly close and reset the static instance.
    // For now, we assume databaseFactoryFfi handles :memory: correctly without needing to close previous.
    // If _database is not null AND we are trying to init with a *different* config, reset.
    // This is a bit tricky with static _database. The simplest is to ensure _database is null before test inits.
    // Or, the service is instantiated per test.

    _database = await openDatabase(path, version: _dbVersion, onCreate: _onCreate, onUpgrade: _onUpgrade);
    return _database!;
  }

  // Method to explicitly close and nullify the database, useful for testing.
  Future<void> closeDb() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    // Article table schema based on Article.toDbMap()
    await db.execute('''
      CREATE TABLE $_articlesTable (
        url TEXT PRIMARY KEY,
        title TEXT,
        description TEXT,
        content TEXT,
        author TEXT,
        sourceName TEXT,
        category TEXT,
        imageUrl TEXT,
        publishedAt TEXT,
        readTime INTEGER,
        isSaved INTEGER DEFAULT 0,
        savedAt TEXT,
        updatedAt TEXT
      )
    ''');
    // Filter table schema based on Filter.toDbMap() and requirements
    await db.execute('''
      CREATE TABLE $_filtersTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        parameters TEXT,
        isAiGenerated INTEGER DEFAULT 0,
        createdAt TEXT,
        isEnabled INTEGER DEFAULT 1,
        updatedAt TEXT
      )
    ''');
    await db.execute('''
      CREATE TABLE $_userSettingsTable (
        id INTEGER PRIMARY KEY DEFAULT 1 CHECK (id = 1),
        $_colTheme TEXT DEFAULT 'system',
        colLanguage TEXT DEFAULT 'en',
        colHasConsented INTEGER DEFAULT 0,
        colPreferredCategories TEXT DEFAULT '[]',
        // colLastSyncTimestamp TEXT, // Replaced
        colLastArticlesSyncTimestamp TEXT,
        colLastFiltersSyncTimestamp TEXT
      )
    ''');
    // Insert default settings row
    await db.insert(
      _userSettingsTable,
      {
        'id': 1, // Explicitly set id to 1
        colTheme: 'system',
        colLanguage: 'en',
        colHasConsented: 0,
        colPreferredCategories: '[]',
        colLastArticlesSyncTimestamp: null,
        colLastFiltersSyncTimestamp: null,
      },
      conflictAlgorithm: ConflictAlgorithm.ignore,
    );
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // For now, just drop and recreate tables.
    // In a production app, you'd use ALTER TABLE for migrations.
    // Example:
    // if (oldVersion < 2) {
    //   await db.execute("ALTER TABLE $_articlesTable ADD COLUMN newColumn TEXT;");
    // }
    await db.execute("DROP TABLE IF EXISTS $_articlesTable");
    await db.execute("DROP TABLE IF EXISTS $_filtersTable");
    await db.execute("DROP TABLE IF EXISTS $_userSettingsTable");
    await _onCreate(db, newVersion);
  }

  // User settings operations
  Future<void> updateSetting(String key, dynamic value) async {
    final db = await database;
    // Ensure the row exists before updating
    await db.insert(
      _userSettingsTable,
      {'id': 1, key: value}, // Provide a default map with id to satisfy insert
      conflictAlgorithm: ConflictAlgorithm.ignore,
    );
    await db.update(
      _userSettingsTable,
      {key: value},
      where: 'id = ?',
      whereArgs: [1],
    );
    // await _hiveService.clearUserSettingsCache(); // Removed Hive call
  }

  Future<Map<String, dynamic>> getUserSettings() async {
    // Removed Hive cache logic, solely fetching from SQLite now.
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _userSettingsTable,
      where: 'id = ?',
      whereArgs: [1],
    );

    if (maps.isNotEmpty) {
      final settings = Map<String, dynamic>.from(maps.first);
      // Ensure preferredCategories is a valid JSON string or default to '[]'
      if (settings[colPreferredCategories] == null || (settings[colPreferredCategories] as String).isEmpty) {
        settings[colPreferredCategories] = '[]';
      }
      return settings;
    } else {
      // This case should ideally not be reached if _onCreate correctly inserts the default row.
      // However, returning default values ensures the app doesn't crash if the row is somehow missing.
      return {
        colTheme: 'system',
        colLanguage: 'en',
        colHasConsented: 0,
        colPreferredCategories: '[]',
        // colLastSyncTimestamp: null, // Replaced
        colLastArticlesSyncTimestamp: null,
        colLastFiltersSyncTimestamp: null,
      };
    }
  }

  // Article operations
  Future<void> saveArticle(Article article, {bool isSaved = false, DateTime? savedAt}) async {
    final db = await database;
    // The Article model's toDbMap now handles isSaved and savedAt logic based on its own fields.
    // The parameters {bool isSaved, DateTime? savedAt} to this service method might be redundant
    // if the Article object passed is already configured correctly.
    // For now, we'll assume the Article object is the source of truth.
    // If these parameters are meant to override the article's state, logic would be needed here.

    // Ensure updatedAt is always set for local DB operations.
    Map<String, dynamic> articleMap = article.copyWith(updatedAt: DateTime.now()).toDbMap();
    await db.insert(_articlesTable, articleMap, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<Article?> getArticle(String url) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_articlesTable, where: 'url = ?', whereArgs: [url]);
    if (maps.isNotEmpty) {
      return Article.fromDbMap(maps.first);
    }
    return null;
  }

  Future<List<Article>> getAllArticles() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_articlesTable);
    return maps.map((map) => Article.fromDbMap(map)).toList();
  }

  Future<List<Article>> getSavedArticles() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_articlesTable, where: 'isSaved = ?', whereArgs: [1]);
    return maps.map((map) => Article.fromDbMap(map)).toList();
  }

  Future<void> updateArticleSavedStatus(String url, bool isSaved, DateTime? savedAt) async {
    final db = await database;
    await db.update(
      _articlesTable,
      {'isSaved': isSaved ? 1 : 0, 'savedAt': isSaved ? (savedAt ?? DateTime.now()).toIso8601String() : null},
      where: 'url = ?',
      whereArgs: [url],
    );
  }

  Future<void> deleteArticle(String url) async {
    final db = await database;
    await db.delete(_articlesTable, where: 'url = ?', whereArgs: [url]);
  }

  Future<void> deleteAllArticles() async {
    final db = await database;
    await db.delete(_articlesTable);
  }

  // Filter operations
  Future<void> saveFilter(Filter filter) async {
    final db = await database;
    await db.insert(_filtersTable, filter.toDbMap(), conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<Filter?> getFilter(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_filtersTable, where: 'id = ?', whereArgs: [id]);
    if (maps.isNotEmpty) {
      return Filter.fromDbMap(maps.first);
    }
    return null;
  }

  Future<List<Filter>> getAllFilters() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_filtersTable);
    return maps.map((map) => Filter.fromDbMap(map)).toList();
  }

  Future<void> deleteFilter(String id) async {
    final db = await database;
    await db.delete(_filtersTable, where: 'id = ?', whereArgs: [id]);
  }

  Future<void> deleteAllFilters() async {
    final db = await database;
    await db.delete(_filtersTable);
  }
}
