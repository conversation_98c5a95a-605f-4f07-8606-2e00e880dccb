import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
// import 'package:hive_flutter/hive_flutter.dart'; // No longer directly needed here if HiveService handles init
import 'firebase_options.dart';

import 'constants/theme_constants.dart';
import 'services/hive_service.dart';
import 'services/sqlite_service.dart'; // Import SQLiteService
import 'repositories/settings_repository.dart'; // Import SettingsRepository
import 'repositories/article_repository.dart';
import 'repositories/filter_repository.dart';
import 'providers/settings_provider.dart'; // Import SettingsProvider
import 'models/article.dart';
import 'providers/auth_provider.dart';
import 'providers/news_provider.dart';
import 'services/news_api_service.dart';
import 'services/firebase_auth_service.dart';
import 'services/firestore_service.dart';
import 'services/local_storage_service.dart';
import 'services/mcp_service.dart';
import 'ui/screens/home_screen.dart';
import 'ui/screens/settings_screen.dart';
import 'ui/screens/saved_articles_screen.dart';
import 'ui/screens/auth/login_screen.dart';
import 'ui/screens/auth/register_screen.dart';
import 'ui/screens/filter_creation_screen.dart';
import 'ui/screens/article_detail_screen.dart';
import 'ui/screens/splash_screen.dart';

// Firebase configuration
// Replace with your actual web API key for production
const String newsApiKey = 'YOUR_NEWS_API_KEY';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  await dotenv.load(fileName: '.env');

  // Depuramos las claves de API
  print('API Key de News API: ${dotenv.env['NEWS_API_KEY']}');

  // Initialize Firebase only if it's not already initialized
  try {
    Firebase.app();
  } catch (e) {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  }

  // Initialize Hive for local data via HiveService
  final hiveService = HiveService();
  await hiveService.initHive();

  // Initialize other services
  final localStorageService = await LocalStorageService.init();
  final firebaseAuthService = FirebaseAuthService();
  final firestoreService = FirestoreService();
  final newsApiService = await NewsApiService.init(
      apiKey: dotenv.env['NEWS_API_KEY'] ?? 'YOUR_API_KEY');
  final mcpService = McpService();
  final sqliteService = SQLiteService(); // Instantiate SQLiteService

  // Initialize Repositories
  final settingsRepository = SettingsRepository(
    sqliteService: sqliteService,
    hiveService: hiveService,
  );
  final articleRepository = ArticleRepository(
    sqliteService: sqliteService,
    settingsRepository: settingsRepository, // Added SettingsRepository dependency
  );
  final filterRepository = FilterRepository(sqliteService: sqliteService);

  // Example: Using a repository method (optional, for testing instantiation)
  // try {
  //   await settingsRepository.getSettings();
  //   print("Settings repository initialized and getSettings called.");
  // } catch (e) {
  //   print("Error calling getSettings from repository: $e");
  // }

  runApp(MyApp(
    authService: firebaseAuthService,
    firestoreService: firestoreService, // Still needed by AuthProvider for some user profile parts
    localStorageService: localStorageService,
    newsApiService: newsApiService,
    mcpService: mcpService,
    settingsRepository: settingsRepository, // Pass repository
    articleRepository: articleRepository,   // Pass repository
    filterRepository: filterRepository,     // Pass repository
  ));
}

class MyApp extends StatelessWidget {
  final FirebaseAuthService authService;
  final FirestoreService firestoreService;
  final LocalStorageService localStorageService;
  final NewsApiService newsApiService;
  final McpService mcpService;
  final SettingsRepository settingsRepository; // Added
  final ArticleRepository articleRepository;   // Added
  final FilterRepository filterRepository;     // Added

  const MyApp({
    super.key,
    required this.authService,
    required this.firestoreService,
    required this.localStorageService,
    required this.newsApiService,
    required this.mcpService,
    required this.settingsRepository, // Added
    required this.articleRepository,   // Added
    required this.filterRepository,     // Added
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => AuthProvider(
            authService: authService,
            firestoreService: firestoreService, // Kept for user profile stuff not yet refactored
            localStorageService: localStorageService,
            articleRepository: articleRepository,   // Injected
            settingsRepository: settingsRepository, // Injected
          ),
        ),
        ChangeNotifierProvider(
          create: (_) => NewsProvider(
            newsApiService: newsApiService,
            mcpService: mcpService,
            firestoreService: firestoreService, // Kept for liked articles (if not fully moved)
            articleRepository: articleRepository,   // Injected
            filterRepository: filterRepository,     // Injected
          ),
        ),
        ChangeNotifierProvider( // Added SettingsProvider
          create: (_) => SettingsProvider(settingsRepository: settingsRepository),
        ),
      ],
      child: MaterialApp(
        title: 'News4Humans',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: const SplashScreen(),
        routes: {
          '/home': (context) => const HomeScreen(),
          '/settings': (context) => const SettingsScreen(),
          '/saved': (context) => const SavedArticlesScreen(),
          '/login': (context) => const LoginScreen(),
          '/register': (context) => const RegisterScreen(),
          '/create_filter': (context) => const FilterCreationScreen(),
        },
        onGenerateRoute: (settings) {
          if (settings.name == '/article') {
            final article = settings.arguments as Article;
            return MaterialPageRoute(
              builder: (context) => ArticleDetailScreen(article: article),
            );
          }
          return null;
        },
      ),
    );
  }
}
