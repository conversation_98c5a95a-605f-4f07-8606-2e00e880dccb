import 'package:mockito/mockito.dart';
import 'package:news4humans/services/sqlite_service.dart';
import 'package:news4humans/services/hive_service.dart';
import 'package:news4humans/repositories/settings_repository.dart';
import 'package:news4humans/repositories/article_repository.dart';
import 'package:news4humans/repositories/filter_repository.dart';
import 'package:news4humans/models/article.dart'; // For Article type hints if needed
import 'package:news4humans/models/filter.dart';   // For Filter type hints if needed

// Mock for SQLiteService
class MockSQLiteService extends Mock implements SQLiteService {
  // If SQLiteService has methods that return Futures of complex non-nullable types
  // and you want to provide default behaviors, you might need to override them here.
  // For most cases, <PERSON><PERSON><PERSON> handles this well, but for things like `Future<Database>`
  // or if methods are called without explicit stubs, providing a default can be helpful.
  // However, for unit testing repositories, we'll be stubbing these methods.
}

// Mock for HiveService
class MockHiveService extends Mock implements HiveService {}

// Mock for SettingsRepository
// Note: SettingsRepository itself might not be directly mocked often if we are testing
// something that USES it. But if another part of the system used SettingsRepository
// and we wanted to mock SettingsRepository, this is how it would be defined.
// For testing SettingsRepository itself, we mock its dependencies (SQLiteService, HiveService).
class MockSettingsRepository extends Mock implements SettingsRepository {}

// Mock for ArticleRepository
class MockArticleRepository extends Mock implements ArticleRepository {}

// Mock for FilterRepository
class MockFilterRepository extends Mock implements FilterRepository {}

// Example of how you might need to provide default Future values for non-nullable returns
// if not always stubbed (though stubbing is preferred in tests where these are dependencies).
// This is more of a general Mockito note than a strict necessity for these specific mocks
// if all interactions are stubbed in tests.
/*
class MockSQLiteServiceWithDefaults extends Mock implements SQLiteService {
  @override
  Future<Map<String, dynamic>> getUserSettings() =>
      super.noSuchMethod(Invocation.method(#getUserSettings, []),
          returnValue: Future.value(<String, dynamic>{
            SQLiteService.colTheme: 'system',
            SQLiteService.colLanguage: 'en',
            SQLiteService.colHasConsented: 0,
            SQLiteService.colPreferredCategories: '[]',
            SQLiteService.colLastArticlesSyncTimestamp: null,
            SQLiteService.colLastFiltersSyncTimestamp: null,
          }),
          returnValueForMissingStub: Future.value(<String, dynamic>{
            SQLiteService.colTheme: 'system',
            // ... other defaults
          }));

  @override
  Future<List<Article>> getAllArticles() =>
      super.noSuchMethod(Invocation.method(#getAllArticles, []),
          returnValue: Future.value(<Article>[]),
          returnValueForMissingStub: Future.value(<Article>[]));

  // ... other methods needing default Future values for non-nullable returns
}
*/
