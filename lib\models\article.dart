class Article {
  final String title;
  final String? description;
  final String? content;
  final String? author;
  final String? source;
  final String? category;
  final String url;
  final String? urlToImage;
  final DateTime publishedAt;
  final int readTime;
  final bool isSaved;
  final DateTime? savedAt;
  final DateTime? updatedAt;

  Article({
    required this.title,
    this.description,
    this.content,
    this.author,
    this.source,
    this.category,
    required this.url,
    this.urlToImage,
    required this.publishedAt,
    required this.readTime,
    this.isSaved = false,
    this.savedAt,
    this.updatedAt,
  });

  Article copyWith({
    String? title,
    String? url,
    DateTime? publishedAt,
    String? author,
    String? source,
    String? urlToImage,
    String? description,
    String? content,
    int? readTime,
    String? category,
    bool? isSaved,
    DateTime? savedAt,
    DateTime? updatedAt,
  }) {
    return Article(
      title: title ?? this.title,
      url: url ?? this.url,
      publishedAt: publishedAt ?? this.publishedAt,
      author: author ?? this.author,
      source: source ?? this.source,
      urlToImage: urlToImage ?? this.urlToImage,
      description: description ?? this.description,
      content: content ?? this.content,
      readTime: readTime ?? this.readTime,
      category: category ?? this.category,
      isSaved: isSaved ?? this.isSaved,
      savedAt: savedAt ?? this.savedAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // For API (JSON) serialization
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'content': content,
      'author': author,
      'source': source,
      'category': category,
      'url': url,
      'urlToImage': urlToImage,
      'publishedAt': publishedAt.toIso8601String(),
      'readTime': readTime,
    };
  }

  // For API (JSON) deserialization
  factory Article.fromJson(Map<String, dynamic> json) {
    final source = json['source'];
    String? sourceName;

    if (source is Map<String, dynamic>) {
      sourceName = source['name'];
    } else if (source is String) {
      sourceName = source;
    }

    return Article(
      title: json['title'] ?? 'No title',
      description: json['description'],
      content: json['content'],
      author: json['author'],
      source: sourceName, // Handles cases where source might be a map or string
      category: json['category'],
      url: json['url'] ?? 'https://example.com', // Provide a default or handle error
      urlToImage: json['urlToImage'],
      publishedAt: json['publishedAt'] is String
          ? DateTime.parse(json['publishedAt'])
          : (json['publishedAt'] is DateTime // Handle if it's already a DateTime
              ? json['publishedAt']
              : DateTime.now()), // Fallback for other types or null
      readTime: json['readTime'] is int
          ? json['readTime']
          : (json['readTime'] is String // Handle if it's a string
              ? int.tryParse(json['readTime']) ?? 3
              : 3), // Fallback
      // isSaved and savedAt are not typically part of API response for new articles
    );
  }

  // For SQLite (Database) serialization
  Map<String, dynamic> toDbMap() {
    return {
      'title': title,
      'description': description,
      'content': content,
      'author': author,
      'sourceName': source, // In DB, it's common to use sourceName
      'category': category,
      'url': url,
      'imageUrl': urlToImage, // In DB, it's common to use imageUrl
      'publishedAt': publishedAt.toIso8601String(),
      'readTime': readTime,
      'isSaved': isSaved ? 1 : 0,
      'savedAt': savedAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      // Fields from previous SQLiteService: isFavorite, isRead, isDeleted, userCategory, notes
      // These were not in the original Article model from file, so they are omitted here.
      // If they need to be persisted, they should be added to the Article class.
      // For now, assuming the schema in SQLiteService will align with this model.
    };
  }

  // For SQLite (Database) deserialization
  factory Article.fromDbMap(Map<String, dynamic> map) {
    return Article(
      title: map['title'] ?? 'No title',
      description: map['description'],
      content: map['content'],
      author: map['author'],
      source: map['sourceName'], // Align with toDbMap key
      category: map['category'],
      url: map['url'] ?? 'https://example.com',
      urlToImage: map['imageUrl'], // Align with toDbMap key
      publishedAt: map['publishedAt'] != null ? DateTime.parse(map['publishedAt']) : DateTime.now(),
      readTime: map['readTime'] ?? 3,
      isSaved: map['isSaved'] == 1,
      savedAt: map['savedAt'] != null ? DateTime.parse(map['savedAt']) : null,
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  @override
  String toString() {
    return 'Article(title: $title, url: $url, isSaved: $isSaved, savedAt: $savedAt, updatedAt: $updatedAt)';
  }
}
