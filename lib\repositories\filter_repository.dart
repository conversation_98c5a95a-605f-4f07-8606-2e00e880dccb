import 'package:news4humans/models/filter.dart';
import 'package:news4humans/services/sqlite_service.dart';

class FilterRepository {
  final SQLiteService _sqliteService;

  FilterRepository({required SQLiteService sqliteService})
      : _sqliteService = sqliteService;

  Future<void> saveFilter(Filter filter) {
    return _sqliteService.saveFilter(filter);
  }

  Future<Filter?> getFilter(String id) {
    return _sqliteService.getFilter(id);
  }

  Future<List<Filter>> getAllFilters() {
    return _sqliteService.getAllFilters();
  }

  Future<void> deleteFilter(String id) {
    return _sqliteService.deleteFilter(id);
  }

  Future<void> deleteAllFilters() {
    return _sqliteService.deleteAllFilters();
  }
}
