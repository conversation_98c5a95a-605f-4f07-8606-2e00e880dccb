import 'dart:convert';

class Filter {
  final String id;
  final String name;
  final String description;
  final Map<String, dynamic> parameters;
  final bool isAiGenerated;
  final DateTime createdAt;
  final bool isEnabled;
  final DateTime? updatedAt;

  Filter({
    required this.id,
    required this.name,
    required this.description,
    required this.parameters,
    this.isAiGenerated = false,
    DateTime? createdAt,
    this.isEnabled = true,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? createdAt ?? DateTime.now(); // Default updatedAt to createdAt or now

  factory Filter.fromJson(Map<String, dynamic> json) {
    return Filter(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      parameters: json['parameters'] as Map<String, dynamic>, // Assuming parameters are already a map
      isAiGenerated: json['is_ai_generated'] as bool? ?? false,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      isEnabled: json['isEnabled'] as bool? ?? true, // Assuming API might send this
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null, // API might not send updatedAt, or handle as created_at
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'parameters': parameters,
      'is_ai_generated': isAiGenerated,
      'created_at': createdAt.toIso8601String(),
      'isEnabled': isEnabled,
    };
  }

  Filter copyWith({
    String? id,
    String? name,
    String? description,
    Map<String, dynamic>? parameters,
    bool? isAiGenerated,
    DateTime? createdAt,
    bool? isEnabled,
    DateTime? updatedAt,
  }) {
    return Filter(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      parameters: parameters ?? this.parameters,
      isAiGenerated: isAiGenerated ?? this.isAiGenerated,
      createdAt: createdAt ?? this.createdAt,
      isEnabled: isEnabled ?? this.isEnabled,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // For SQLite (Database) serialization
  Map<String, dynamic> toDbMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'parameters': jsonEncode(parameters), // Convert Map to JSON String
      'isAiGenerated': isAiGenerated ? 1 : 0, // Convert bool to int
      'createdAt': createdAt.toIso8601String(), // Convert DateTime to String
      'isEnabled': isEnabled ? 1 : 0, // Convert bool to int
      'updatedAt': (updatedAt ?? DateTime.now()).toIso8601String(), // Ensure updatedAt is set
    };
  }

  // For SQLite (Database) deserialization
  factory Filter.fromDbMap(Map<String, dynamic> map) {
    return Filter(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String? ?? '', // Handle potential null from DB
      parameters: map['parameters'] != null
          ? jsonDecode(map['parameters'] as String) as Map<String, dynamic> // Convert JSON String to Map
          : <String, dynamic>{}, // Default to empty map if null
      isAiGenerated: (map['isAiGenerated'] as int? ?? 0) == 1, // Convert int to bool
      createdAt: map['createdAt'] != null
          ? DateTime.parse(map['createdAt'] as String) // Convert String to DateTime
          : DateTime.now(), // Default if null
      isEnabled: (map['isEnabled'] as int? ?? 1) == 1, // Convert int to bool, default true
      updatedAt: map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'] as String)
          : DateTime.now(), // Default to now if null from DB (e.g. older records)
    );
  }

  // Helper methods to create common filters
  static Filter topHeadlines({
    required String id,
    String? name,
    String? country,
    String? category,
    String? q,
    bool? isEnabled, // Allow setting isEnabled for common filters
  }) {
    return Filter(
      id: id,
      name:
          name ??
          (category != null
              ? 'Top ${category.toUpperCase()} Headlines'
              : 'Top Headlines'),
      description: 'Latest news from ${country?.toUpperCase() ?? 'the world'}',
      parameters: {'country': country, 'category': category, 'q': q},
      isEnabled: isEnabled ?? true,
    );
  }

  static Filter everything({
    required String id,
    String? name,
    String? q,
    List<String>? sources,
    String? domains,
    String? language,
    String? sortBy,
    bool? isEnabled, // Allow setting isEnabled for common filters
  }) {
    return Filter(
      id: id,
      name: name ?? (q != null ? 'News about "$q"' : 'Latest News'),
      description: 'Comprehensive coverage from various sources',
      parameters: {
        'q': q,
        'sources': sources?.join(','),
        'domains': domains,
        'language': language,
        'sortBy': sortBy ?? 'publishedAt', // publishedAt, relevancy, popularity
      },
      isEnabled: isEnabled ?? true,
    );
  }

  // Create AI-generated filter
  static Filter aiGenerated({
    required String id,
    required String name,
    required String description,
    required Map<String, dynamic> parameters,
    bool? isEnabled, // Allow setting isEnabled for AI filters
  }) {
    return Filter(
      id: id,
      name: name,
      description: description,
      parameters: parameters,
      isAiGenerated: true,
      isEnabled: isEnabled ?? true,
    );
  }
}
